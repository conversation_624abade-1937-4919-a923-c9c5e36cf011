import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Generates a unique ID for template elements
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as { [key: string]: any }
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj as T
  }
  return obj
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Format date in relative time (e.g., "2 days ago")
 */
export function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`
  return `${Math.floor(diffInSeconds / 31536000)} years ago`
}

/**
 * Validate MediaWiki template name
 */
export function isValidTemplateName(name: string): boolean {
  // MediaWiki template names cannot contain certain characters
  const invalidChars = /[<>[\]{}|#]/
  return !invalidChars.test(name) && name.trim().length > 0
}

/**
 * Sanitize HTML content
 */
export function sanitizeHtml(html: string): string {
  // Basic HTML sanitization - in production, use a proper library like DOMPurify
  return html
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
}

/**
 * Convert hex color to RGB
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

/**
 * Convert RGB to hex color
 */
export function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

/**
 * Get contrast color (black or white) for a given background color
 */
export function getContrastColor(hexColor: string): string {
  const rgb = hexToRgb(hexColor)
  if (!rgb) return '#000000'
  
  // Calculate luminance
  const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255
  return luminance > 0.5 ? '#000000' : '#ffffff'
}

/**
 * Validate CSS property value
 */
export function isValidCSSValue(property: string, value: string): boolean {
  // Basic CSS validation - in production, use a proper CSS parser
  if (!value || value.trim() === '') return false
  
  switch (property) {
    case 'color':
    case 'backgroundColor':
      return /^(#[0-9a-fA-F]{3,6}|rgb\(|rgba\(|hsl\(|hsla\(|\w+)/.test(value)
    case 'fontSize':
      return /^\d+(\.\d+)?(px|em|rem|%|pt|pc|in|cm|mm|ex|ch|vw|vh|vmin|vmax)$/.test(value)
    case 'padding':
    case 'margin':
      return /^(\d+(\.\d+)?(px|em|rem|%|pt|pc|in|cm|mm|ex|ch|vw|vh|vmin|vmax)(\s+\d+(\.\d+)?(px|em|rem|%|pt|pc|in|cm|mm|ex|ch|vw|vh|vmin|vmax)){0,3}|auto|inherit|initial|unset)$/.test(value)
    default:
      return true // Allow other properties for now
  }
}

/**
 * Extract MediaWiki parameters from wikitext
 */
export function extractMediaWikiParameters(wikitext: string): string[] {
  const parameterRegex = /\{\{\{([^}|]+)(\|[^}]*)?\}\}\}/g
  const parameters: string[] = []
  let match
  
  while ((match = parameterRegex.exec(wikitext)) !== null) {
    const paramName = match[1].trim()
    if (!parameters.includes(paramName)) {
      parameters.push(paramName)
    }
  }
  
  return parameters
}

/**
 * Validate MediaWiki wikitext syntax
 */
export function validateWikitext(wikitext: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // Check for balanced braces
  const openBraces = (wikitext.match(/\{\{/g) || []).length
  const closeBraces = (wikitext.match(/\}\}/g) || []).length
  if (openBraces !== closeBraces) {
    errors.push('Unbalanced template braces {{ }}')
  }
  
  // Check for balanced brackets
  const openBrackets = (wikitext.match(/\[\[/g) || []).length
  const closeBrackets = (wikitext.match(/\]\]/g) || []).length
  if (openBrackets !== closeBrackets) {
    errors.push('Unbalanced link brackets [[ ]]')
  }
  
  // Check for balanced table syntax
  const tableStarts = (wikitext.match(/\{\|/g) || []).length
  const tableEnds = (wikitext.match(/\|\}/g) || []).length
  if (tableStarts !== tableEnds) {
    errors.push('Unbalanced table syntax {| |}')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Convert camelCase to kebab-case
 */
export function camelToKebab(str: string): string {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
}

/**
 * Convert kebab-case to camelCase
 */
export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength - 3) + '...'
}
