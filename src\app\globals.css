@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }
  
  body {
    @apply text-gray-900 antialiased;
  }
}

@layer components {
  /* Custom component styles */
  .btn-primary {
    @apply bg-mw-blue-600 hover:bg-mw-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-mw-blue-500 focus:border-transparent;
  }
  
  .editor-panel {
    @apply bg-white border-r border-gray-200 h-full overflow-y-auto;
  }
  
  .canvas-area {
    @apply flex-1 bg-gray-50 relative overflow-auto;
  }
  
  .component-palette {
    @apply w-64 bg-white border-l border-gray-200 h-full overflow-y-auto;
  }
}

@layer utilities {
  .drag-handle {
    cursor: grab;
  }
  
  .drag-handle:active {
    cursor: grabbing;
  }
  
  .drop-zone {
    @apply border-2 border-dashed border-mw-blue-300 bg-mw-blue-50 rounded-lg;
  }
  
  .drop-zone-active {
    @apply border-mw-blue-500 bg-mw-blue-100;
  }
}
