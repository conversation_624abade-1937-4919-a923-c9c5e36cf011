'use client'

import { useState } from 'react'
import { 
  FileText, 
  Palette, 
  Eye, 
  Download, 
  Upload, 
  Save,
  Undo,
  Redo,
  Grid,
  Smartphone,
  Tablet,
  Monitor
} from 'lucide-react'

export default function EditorPage() {
  const [selectedTool, setSelectedTool] = useState<string>('select')
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [showGrid, setShowGrid] = useState(true)

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header Toolbar */}
      <header className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <FileText className="h-6 w-6 text-mw-blue-600 mr-2" />
              <h1 className="text-lg font-semibold text-gray-900">Template Editor</h1>
            </div>
            
            {/* File Operations */}
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded">
                <Upload className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded">
                <Save className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded">
                <Download className="h-4 w-4" />
              </button>
            </div>

            {/* Edit Operations */}
            <div className="flex items-center space-x-2 border-l border-gray-200 pl-4">
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded">
                <Undo className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded">
                <Redo className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Preview Controls */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowGrid(!showGrid)}
                className={`p-2 rounded ${showGrid ? 'bg-mw-blue-100 text-mw-blue-600' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'}`}
              >
                <Grid className="h-4 w-4" />
              </button>
            </div>

            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setPreviewMode('desktop')}
                className={`p-2 rounded ${previewMode === 'desktop' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
              >
                <Monitor className="h-4 w-4" />
              </button>
              <button
                onClick={() => setPreviewMode('tablet')}
                className={`p-2 rounded ${previewMode === 'tablet' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
              >
                <Tablet className="h-4 w-4" />
              </button>
              <button
                onClick={() => setPreviewMode('mobile')}
                className={`p-2 rounded ${previewMode === 'mobile' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
              >
                <Smartphone className="h-4 w-4" />
              </button>
            </div>

            <button className="btn-primary flex items-center">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </button>
          </div>
        </div>
      </header>

      {/* Main Editor Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Component Palette */}
        <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-sm font-semibold text-gray-900 mb-3">Components</h2>
            <div className="space-y-2">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Basic</div>
              <ComponentPaletteItem icon="📝" name="Text" type="text" />
              <ComponentPaletteItem icon="🖼️" name="Image" type="image" />
              <ComponentPaletteItem icon="📋" name="Table" type="table" />
              <ComponentPaletteItem icon="📦" name="Container" type="container" />
            </div>
            
            <div className="space-y-2 mt-4">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Templates</div>
              <ComponentPaletteItem icon="ℹ️" name="Infobox" type="infobox" />
              <ComponentPaletteItem icon="🧭" name="Navigation" type="navbox" />
              <ComponentPaletteItem icon="📖" name="Citation" type="citation" />
            </div>
          </div>
        </div>

        {/* Center - Canvas Area */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1 relative overflow-auto bg-gray-50">
            {/* Grid Background */}
            {showGrid && (
              <div 
                className="absolute inset-0 opacity-20"
                style={{
                  backgroundImage: `
                    linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                    linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
                  `,
                  backgroundSize: '20px 20px'
                }}
              />
            )}
            
            {/* Canvas */}
            <div className={`
              mx-auto my-8 bg-white shadow-lg rounded-lg overflow-hidden
              ${previewMode === 'desktop' ? 'max-w-4xl' : ''}
              ${previewMode === 'tablet' ? 'max-w-2xl' : ''}
              ${previewMode === 'mobile' ? 'max-w-sm' : ''}
            `}>
              <div className="min-h-96 p-8">
                {/* Template Canvas Content */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium mb-2">Start Building Your Template</h3>
                  <p className="text-sm">Drag components from the left panel to begin creating your MediaWiki template.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Properties Panel */}
        <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-sm font-semibold text-gray-900 mb-3">Properties</h2>
            <div className="text-sm text-gray-500">
              Select an element to edit its properties
            </div>
          </div>
          
          <div className="flex-1 p-4">
            {/* Properties will be dynamically loaded here */}
            <div className="space-y-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Element Type
                </label>
                <div className="text-sm text-gray-500">No element selected</div>
              </div>
            </div>
          </div>

          {/* Code Preview Tabs */}
          <div className="border-t border-gray-200">
            <div className="flex">
              <button className="flex-1 px-4 py-2 text-xs font-medium text-gray-700 bg-gray-50 border-r border-gray-200">
                Wikitext
              </button>
              <button className="flex-1 px-4 py-2 text-xs font-medium text-gray-500 hover:text-gray-700">
                CSS
              </button>
            </div>
            <div className="p-4 bg-gray-50 max-h-48 overflow-y-auto">
              <pre className="text-xs text-gray-600 font-mono">
                {`<!-- Generated wikitext will appear here -->`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface ComponentPaletteItemProps {
  icon: string
  name: string
  type: string
}

function ComponentPaletteItem({ icon, name, type }: ComponentPaletteItemProps) {
  return (
    <div 
      className="flex items-center p-2 rounded-lg border border-gray-200 hover:border-mw-blue-300 hover:bg-mw-blue-50 cursor-pointer transition-colors"
      draggable
      onDragStart={(e) => {
        e.dataTransfer.setData('application/json', JSON.stringify({ type, name }))
      }}
    >
      <span className="text-lg mr-3">{icon}</span>
      <span className="text-sm font-medium text-gray-700">{name}</span>
    </div>
  )
}
