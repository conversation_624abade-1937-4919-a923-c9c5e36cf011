import Link from 'next/link'
import { FileText, Eye, Download, Edit, Star, Clock } from 'lucide-react'

export default function GalleryPage() {
  const templateCategories = [
    { id: 'infobox', name: 'Infoboxes', count: 12, icon: 'ℹ️' },
    { id: 'navigation', name: 'Navigation', count: 8, icon: '🧭' },
    { id: 'citation', name: 'Citations', count: 6, icon: '📖' },
    { id: 'maintenance', name: 'Maintenance', count: 4, icon: '🔧' },
    { id: 'userbox', name: 'Userboxes', count: 10, icon: '👤' },
    { id: 'custom', name: 'Custom', count: 15, icon: '🎨' },
  ]

  const featuredTemplates = [
    {
      id: '1',
      name: 'Person Infobox',
      description: 'A comprehensive infobox template for biographical articles',
      category: 'infobox',
      author: 'WikiEditor',
      downloads: 1250,
      rating: 4.8,
      preview: '/api/templates/1/preview.png',
      lastUpdated: '2 days ago'
    },
    {
      id: '2',
      name: 'Navigation Box',
      description: 'Clean navigation template for article series',
      category: 'navigation',
      author: 'TemplateBuilder',
      downloads: 890,
      rating: 4.6,
      preview: '/api/templates/2/preview.png',
      lastUpdated: '1 week ago'
    },
    {
      id: '3',
      name: 'Book Citation',
      description: 'Standard citation template for books and publications',
      category: 'citation',
      author: 'CitationMaster',
      downloads: 2100,
      rating: 4.9,
      preview: '/api/templates/3/preview.png',
      lastUpdated: '3 days ago'
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <FileText className="h-8 w-8 text-mw-blue-600 mr-3" />
                <h1 className="text-2xl font-bold text-gray-900">Template Gallery</h1>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/editor" className="btn-primary">
                Create New Template
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search templates..."
                className="input-field"
              />
            </div>
            <div className="flex gap-2">
              <select className="input-field w-auto">
                <option>All Categories</option>
                {templateCategories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              <select className="input-field w-auto">
                <option>Sort by Popular</option>
                <option>Sort by Recent</option>
                <option>Sort by Rating</option>
                <option>Sort by Name</option>
              </select>
            </div>
          </div>
        </div>

        {/* Categories Grid */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Browse by Category</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {templateCategories.map(category => (
              <Link
                key={category.id}
                href={`/gallery?category=${category.id}`}
                className="card hover:shadow-md transition-shadow cursor-pointer text-center"
              >
                <div className="text-3xl mb-2">{category.icon}</div>
                <h3 className="font-semibold text-gray-900 mb-1">{category.name}</h3>
                <p className="text-sm text-gray-500">{category.count} templates</p>
              </Link>
            ))}
          </div>
        </section>

        {/* Featured Templates */}
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Featured Templates</h2>
            <Link href="/gallery?featured=true" className="text-mw-blue-600 hover:text-mw-blue-700 font-medium">
              View all featured →
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredTemplates.map(template => (
              <TemplateCard key={template.id} template={template} />
            ))}
          </div>
        </section>

        {/* Recent Templates */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Recently Added</h2>
            <Link href="/gallery?sort=recent" className="text-mw-blue-600 hover:text-mw-blue-700 font-medium">
              View all recent →
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Placeholder for recent templates */}
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="card">
                <div className="aspect-video bg-gray-200 rounded-lg mb-3 flex items-center justify-center">
                  <FileText className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-1">Template {index + 1}</h3>
                <p className="text-sm text-gray-500 mb-2">Sample template description</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>by Author</span>
                  <span>1 day ago</span>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>
    </div>
  )
}

interface TemplateCardProps {
  template: {
    id: string
    name: string
    description: string
    category: string
    author: string
    downloads: number
    rating: number
    preview: string
    lastUpdated: string
  }
}

function TemplateCard({ template }: TemplateCardProps) {
  return (
    <div className="card hover:shadow-lg transition-shadow">
      {/* Preview Image */}
      <div className="aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
        <FileText className="h-12 w-12 text-gray-400" />
      </div>
      
      {/* Template Info */}
      <div className="mb-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold text-gray-900 text-lg">{template.name}</h3>
          <div className="flex items-center text-yellow-500">
            <Star className="h-4 w-4 fill-current" />
            <span className="text-sm text-gray-600 ml-1">{template.rating}</span>
          </div>
        </div>
        <p className="text-gray-600 text-sm mb-3">{template.description}</p>
        
        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
          <span>by {template.author}</span>
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            {template.lastUpdated}
          </div>
        </div>
        
        <div className="flex items-center text-xs text-gray-500">
          <Download className="h-3 w-3 mr-1" />
          {template.downloads.toLocaleString()} downloads
        </div>
      </div>
      
      {/* Actions */}
      <div className="flex gap-2">
        <Link 
          href={`/gallery/${template.id}`}
          className="flex-1 btn-secondary text-center text-sm py-2"
        >
          <Eye className="h-4 w-4 inline mr-1" />
          Preview
        </Link>
        <Link 
          href={`/editor?template=${template.id}`}
          className="flex-1 btn-primary text-center text-sm py-2"
        >
          <Edit className="h-4 w-4 inline mr-1" />
          Edit
        </Link>
      </div>
    </div>
  )
}
