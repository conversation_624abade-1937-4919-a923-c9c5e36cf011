// CSS Generation Engine - Converts visual styles to MediaWiki-compatible CSS

import { Template, TemplateElement, CSSStyles, CSSOutput } from '@/types/template'

export class CSSGenerationEngine {
  /**
   * Generates complete CSS output for a template
   */
  static generateCSS(template: Template): CSSOutput {
    const styles = this.generateStyles(template.elements)
    const mediaQueries = this.generateMediaQueries(template.elements)
    const variables = this.generateCSSVariables(template)

    return {
      styles,
      mediaQueries,
      variables
    }
  }

  /**
   * Generates the main CSS styles
   */
  private static generateStyles(elements: TemplateElement[]): string {
    const cssRules: string[] = []
    
    // Add base template styles
    cssRules.push(this.generateBaseStyles())
    
    // Generate styles for each element
    elements.forEach(element => {
      const elementCSS = this.generateElementCSS(element)
      if (elementCSS) {
        cssRules.push(elementCSS)
      }
    })
    
    return cssRules.join('\n\n')
  }

  /**
   * Generates base styles for all templates
   */
  private static generateBaseStyles(): string {
    return `/* MediaWiki Template Base Styles */
.template-container {
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Lato, Helvetica, Arial, sans-serif;
  line-height: 1.6;
}

.template-container * {
  box-sizing: inherit;
}

/* Responsive images */
.template-container img {
  max-width: 100%;
  height: auto;
}

/* Clear floats */
.template-container::after {
  content: "";
  display: table;
  clear: both;
}`
  }

  /**
   * Generates CSS for a single element and its children
   */
  private static generateElementCSS(element: TemplateElement): string {
    const cssRules: string[] = []
    
    // Generate CSS for this element
    const elementRule = this.generateElementRule(element)
    if (elementRule) {
      cssRules.push(elementRule)
    }
    
    // Generate CSS for child elements
    if (element.children) {
      element.children.forEach(child => {
        const childCSS = this.generateElementCSS(child)
        if (childCSS) {
          cssRules.push(childCSS)
        }
      })
    }
    
    return cssRules.join('\n\n')
  }

  /**
   * Generates CSS rule for a single element
   */
  private static generateElementRule(element: TemplateElement): string {
    const className = this.getElementClassName(element)
    const styles = this.convertStylesToCSS(element.styles)
    
    if (!styles) return ''
    
    let cssRule = `.${className} {\n${styles}\n}`
    
    // Add element-specific styles
    const specificStyles = this.getElementSpecificStyles(element)
    if (specificStyles) {
      cssRule += `\n\n${specificStyles}`
    }
    
    return cssRule
  }

  /**
   * Gets the CSS class name for an element
   */
  private static getElementClassName(element: TemplateElement): string {
    if (element.properties.cssClass) {
      return element.properties.cssClass
    }
    
    return `${element.type}-${element.id}`
  }

  /**
   * Converts CSSStyles object to CSS string
   */
  private static convertStylesToCSS(styles: CSSStyles): string {
    const cssProperties: string[] = []
    
    Object.entries(styles).forEach(([property, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        const cssProperty = this.camelToKebabCase(property)
        cssProperties.push(`  ${cssProperty}: ${value};`)
      }
    })
    
    return cssProperties.join('\n')
  }

  /**
   * Converts camelCase to kebab-case
   */
  private static camelToKebabCase(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
  }

  /**
   * Gets element-specific CSS styles
   */
  private static getElementSpecificStyles(element: TemplateElement): string {
    switch (element.type) {
      case 'infobox':
        return this.generateInfoboxStyles(element)
      case 'navbox':
        return this.generateNavboxStyles(element)
      case 'table':
        return this.generateTableStyles(element)
      case 'image':
        return this.generateImageStyles(element)
      default:
        return ''
    }
  }

  /**
   * Generates infobox-specific styles
   */
  private static generateInfoboxStyles(element: TemplateElement): string {
    const className = this.getElementClassName(element)
    
    return `.${className} {
  border: 1px solid #a2a9b1;
  border-spacing: 3px;
  background-color: #f8f9fa;
  color: black;
  margin: 0.5em 0 0.5em 1em;
  padding: 0.2em;
  float: right;
  clear: right;
  font-size: 88%;
  line-height: 1.5em;
  width: 22em;
}

.${className} th {
  background-color: #eaecf0;
  text-align: center;
  font-weight: bold;
  padding: 0.2em 0.4em;
}

.${className} td {
  padding: 0.2em 0.4em;
  vertical-align: top;
}

.${className} .infobox-label {
  font-weight: bold;
  text-align: right;
  vertical-align: top;
  width: 30%;
}

.${className} .infobox-data {
  vertical-align: top;
}`
  }

  /**
   * Generates navbox-specific styles
   */
  private static generateNavboxStyles(element: TemplateElement): string {
    const className = this.getElementClassName(element)
    
    return `.${className} {
  border: 1px solid #a2a9b1;
  width: 100%;
  clear: both;
  font-size: 88%;
  text-align: center;
  padding: 1px;
  margin: 1em auto 0;
  background-color: #fdfdfd;
}

.${className} .navbox-title {
  background-color: #ccccff;
  font-size: 114%;
  margin: 0 4em;
  padding: 0 0.25em;
  font-weight: bold;
}

.${className} .navbox-group {
  background-color: #ddddff;
  padding: 0 0.75em;
  text-align: right;
  font-weight: bold;
  white-space: nowrap;
  width: 1%;
}

.${className} .navbox-list {
  text-align: left;
  border-left: 2px solid #fdfdfd;
  width: 100%;
  padding: 0 0.25em;
}`
  }

  /**
   * Generates table-specific styles
   */
  private static generateTableStyles(element: TemplateElement): string {
    const className = this.getElementClassName(element)
    
    return `.${className} {
  border-collapse: collapse;
  margin: 1em 0;
  background-color: transparent;
}

.${className} th,
.${className} td {
  border: 1px solid #a2a9b1;
  padding: 0.2em 0.4em;
}

.${className} th {
  background-color: #eaecf0;
  font-weight: bold;
  text-align: center;
}

.${className} tr:nth-child(even) {
  background-color: #f8f9fa;
}`
  }

  /**
   * Generates image-specific styles
   */
  private static generateImageStyles(element: TemplateElement): string {
    const className = this.getElementClassName(element)
    
    return `.${className} {
  max-width: 100%;
  height: auto;
}

.${className} .image-caption {
  font-size: 94%;
  text-align: center;
  margin-top: 0.5em;
  color: #54595d;
}`
  }

  /**
   * Generates responsive media queries
   */
  private static generateMediaQueries(elements: TemplateElement[]): string {
    const mediaQueries: string[] = []
    
    // Mobile styles
    mediaQueries.push(`/* Mobile Styles */
@media screen and (max-width: 768px) {
  .infobox {
    width: 100% !important;
    margin: 0.5em 0 !important;
    float: none !important;
    clear: none !important;
  }
  
  .navbox {
    font-size: 85%;
  }
  
  .navbox-title {
    margin: 0 1em !important;
  }
  
  .template-container table {
    font-size: 85%;
  }
  
  .template-container th,
  .template-container td {
    padding: 0.1em 0.2em;
  }
}`)

    // Tablet styles
    mediaQueries.push(`/* Tablet Styles */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .infobox {
    width: 280px;
  }
  
  .navbox {
    font-size: 90%;
  }
}`)

    // Print styles
    mediaQueries.push(`/* Print Styles */
@media print {
  .navbox {
    display: none !important;
  }
  
  .infobox {
    border: 1px solid #000 !important;
    background: white !important;
    color: black !important;
  }
  
  .template-container {
    color: black !important;
    background: white !important;
  }
}`)
    
    return mediaQueries.join('\n\n')
  }

  /**
   * Generates CSS custom properties (variables)
   */
  private static generateCSSVariables(template: Template): string {
    const variables: string[] = []
    
    // Extract common colors and values
    const colors = this.extractColors(template.elements)
    const fonts = this.extractFonts(template.elements)
    const spacing = this.extractSpacing(template.elements)
    
    variables.push(':root {')
    
    // Color variables
    colors.forEach((color, index) => {
      variables.push(`  --template-color-${index + 1}: ${color};`)
    })
    
    // Font variables
    fonts.forEach((font, index) => {
      variables.push(`  --template-font-${index + 1}: ${font};`)
    })
    
    // Spacing variables
    spacing.forEach((space, index) => {
      variables.push(`  --template-spacing-${index + 1}: ${space};`)
    })
    
    // Common template variables
    variables.push('  --template-border-color: #a2a9b1;')
    variables.push('  --template-background: #f8f9fa;')
    variables.push('  --template-text-color: #202122;')
    variables.push('  --template-link-color: #0645ad;')
    variables.push('  --template-border-radius: 3px;')
    
    variables.push('}')
    
    return variables.join('\n')
  }

  /**
   * Extracts unique colors from template elements
   */
  private static extractColors(elements: TemplateElement[]): string[] {
    const colors = new Set<string>()
    
    const extractFromElement = (element: TemplateElement) => {
      if (element.styles.backgroundColor) {
        colors.add(element.styles.backgroundColor)
      }
      if (element.styles.color) {
        colors.add(element.styles.color)
      }
      if (element.styles.border && element.styles.border.includes('#')) {
        const colorMatch = element.styles.border.match(/#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/)
        if (colorMatch) {
          colors.add(colorMatch[0])
        }
      }
      
      if (element.children) {
        element.children.forEach(extractFromElement)
      }
    }
    
    elements.forEach(extractFromElement)
    return Array.from(colors)
  }

  /**
   * Extracts unique fonts from template elements
   */
  private static extractFonts(elements: TemplateElement[]): string[] {
    const fonts = new Set<string>()
    
    const extractFromElement = (element: TemplateElement) => {
      if (element.styles.fontFamily) {
        fonts.add(element.styles.fontFamily)
      }
      
      if (element.children) {
        element.children.forEach(extractFromElement)
      }
    }
    
    elements.forEach(extractFromElement)
    return Array.from(fonts)
  }

  /**
   * Extracts unique spacing values from template elements
   */
  private static extractSpacing(elements: TemplateElement[]): string[] {
    const spacing = new Set<string>()
    
    const extractFromElement = (element: TemplateElement) => {
      if (element.styles.padding) {
        spacing.add(element.styles.padding)
      }
      if (element.styles.margin) {
        spacing.add(element.styles.margin)
      }
      
      if (element.children) {
        element.children.forEach(extractFromElement)
      }
    }
    
    elements.forEach(extractFromElement)
    return Array.from(spacing)
  }

  /**
   * Minifies CSS by removing unnecessary whitespace and comments
   */
  static minifyCSS(css: string): string {
    return css
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/;\s*}/g, '}') // Remove semicolon before closing brace
      .replace(/\s*{\s*/g, '{') // Remove spaces around opening brace
      .replace(/;\s*/g, ';') // Remove spaces after semicolon
      .replace(/,\s*/g, ',') // Remove spaces after comma
      .trim()
  }
}
