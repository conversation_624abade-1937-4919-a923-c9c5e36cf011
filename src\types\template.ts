// Core template types for the MediaWiki Template Builder

export interface TemplateElement {
  id: string
  type: TemplateElementType
  name: string
  properties: Record<string, any>
  styles: CSSStyles
  children?: TemplateElement[]
  position: Position
  size: Size
}

export type TemplateElementType = 
  | 'container'
  | 'text'
  | 'image'
  | 'table'
  | 'list'
  | 'infobox'
  | 'navbox'
  | 'citation'
  | 'category'
  | 'parameter'
  | 'conditional'

export interface Position {
  x: number
  y: number
}

export interface Size {
  width: number | 'auto'
  height: number | 'auto'
}

export interface CSSStyles {
  backgroundColor?: string
  color?: string
  fontSize?: string
  fontWeight?: string
  fontFamily?: string
  padding?: string
  margin?: string
  border?: string
  borderRadius?: string
  textAlign?: 'left' | 'center' | 'right' | 'justify'
  display?: string
  flexDirection?: 'row' | 'column'
  justifyContent?: string
  alignItems?: string
  width?: string
  height?: string
  minWidth?: string
  maxWidth?: string
  [key: string]: any
}

export interface Template {
  id: string
  name: string
  description: string
  category: TemplateCategory
  elements: TemplateElement[]
  parameters: TemplateParameter[]
  metadata: TemplateMetadata
  createdAt: Date
  updatedAt: Date
}

export type TemplateCategory = 
  | 'infobox'
  | 'navigation'
  | 'citation'
  | 'maintenance'
  | 'userbox'
  | 'custom'

export interface TemplateParameter {
  name: string
  type: ParameterType
  required: boolean
  defaultValue?: string
  description: string
  validation?: ParameterValidation
}

export type ParameterType = 
  | 'string'
  | 'number'
  | 'boolean'
  | 'date'
  | 'url'
  | 'image'
  | 'category'
  | 'template'

export interface ParameterValidation {
  pattern?: string
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  options?: string[]
}

export interface TemplateMetadata {
  author: string
  version: string
  tags: string[]
  documentation: string
  examples: TemplateExample[]
  compatibility: MediaWikiCompatibility
}

export interface TemplateExample {
  name: string
  description: string
  parameters: Record<string, any>
  expectedOutput: string
}

export interface MediaWikiCompatibility {
  minVersion: string
  maxVersion?: string
  extensions: string[]
}

// Generated output types
export interface GeneratedTemplate {
  wikitext: string
  css: string
  documentation: string
  parameters: TemplateParameter[]
}

export interface WikitextOutput {
  templateCode: string
  documentationPage: string
  categoryCode: string
}

export interface CSSOutput {
  styles: string
  mediaQueries: string
  variables: string
}

// Editor state types
export interface EditorState {
  currentTemplate: Template | null
  selectedElement: TemplateElement | null
  draggedElement: TemplateElement | null
  clipboard: TemplateElement | null
  history: EditorHistory
  settings: EditorSettings
}

export interface EditorHistory {
  past: Template[]
  present: Template
  future: Template[]
}

export interface EditorSettings {
  gridSize: number
  snapToGrid: boolean
  showGrid: boolean
  previewMode: 'desktop' | 'tablet' | 'mobile'
  theme: 'light' | 'dark'
}

// Component library types
export interface ComponentDefinition {
  id: string
  name: string
  category: string
  description: string
  icon: string
  defaultProperties: Record<string, any>
  defaultStyles: CSSStyles
  template: TemplateElement
  previewImage?: string
}

// Import/Export types
export interface ImportedTemplate {
  source: 'mediawiki' | 'file' | 'url'
  originalCode: string
  parsedTemplate: Template
  warnings: string[]
  errors: string[]
}

export interface ExportOptions {
  format: 'wikitext' | 'css' | 'both' | 'json'
  includeDocumentation: boolean
  includeExamples: boolean
  minifyCSS: boolean
  addComments: boolean
}
