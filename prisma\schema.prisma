generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String   // TemplateCategory enum as string
  elements    String   // JSON string of TemplateElement[]
  parameters  String   // JSON string of TemplateParameter[]
  metadata    String   // JSON string of TemplateMetadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  // Relations
  author      User?    @relation("UserTemplates", fields: [authorId], references: [id])
  authorId    String?
  // Template sharing and collaboration
  isPublic    Boolean  @default(false)
  tags        String?  // JSON array of tags
  @@map("templates")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  // Relations
  templates Template[]         @relation("UserTemplates")
  componentLibraries ComponentLibrary[] @relation("UserComponentLibraries")
  @@map("users")
}

model ComponentLibrary {
  id            String   @id @default(cuid())
  name          String
  category      String
  description   String
  icon          String
  definition    String   // JSON string of ComponentDefinition
  isBuiltIn     Boolean  @default(false)
  downloadCount Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  // Relations
  author        User?    @relation("UserComponentLibraries", fields: [authorId], references: [id])
  authorId      String?
  @@map("component_library")
}

model TemplateUsage {
  id         String   @id @default(cuid())
  templateId String
  userId     String?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())
  @@map("template_usage")
}

model ExportLog {
  id         String   @id @default(cuid())
  templateId String
  format     String   // ExportFormat
  userId     String?
  ipAddress  String?
  createdAt  DateTime @default(now())
  @@map("export_logs")
}