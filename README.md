# MediaWiki Template Builder

A visual tool for creating beautiful MediaWiki templates and their corresponding CSS code through an intuitive drag-and-drop interface.

## Features

- 🎨 **Visual Template Designer** - Drag-and-drop interface for building MediaWiki templates
- 📝 **Wikitext Generation** - Automatically generates clean, well-structured MediaWiki template code
- 🎯 **CSS Generation** - Creates corresponding CSS styling code with responsive design support
- 👀 **Real-time Preview** - See how templates will look when rendered in MediaWiki
- 📦 **Template Library** - Pre-built components for infoboxes, navigation boxes, citations, etc.
- 📤 **Import/Export** - Import existing templates for editing and export both wikitext and CSS
- 📱 **Responsive Design** - Templates work across different screen sizes

## Prerequisites

Before setting up the project, ensure you have the following installed:

1. **Node.js** (v18 or higher) - Download from [nodejs.org](https://nodejs.org/)
2. **Git** - For version control
3. **VS Code** (recommended) - For development

## Quick Start

### 1. Install Node.js

Download and install Node.js from [https://nodejs.org/](https://nodejs.org/)
Choose the LTS version for stability.

### 2. Verify Installation

Open a new terminal/command prompt and run:
```bash
node --version
npm --version
```

### 3. Initialize the Project

Once Node.js is installed, run these commands in your project directory:

```bash
# Create Next.js project with TypeScript and Tailwind
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Install additional dependencies
npm install @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities
npm install @monaco-editor/react
npm install zustand
npm install react-hook-form @hookform/resolvers zod
npm install framer-motion
npm install prisma @prisma/client
npm install lucide-react

# Install development dependencies
npm install -D @types/node prisma
```

### 4. Project Structure

```
mediawiki-template-builder/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── api/               # API routes
│   │   ├── editor/            # Visual editor page
│   │   ├── gallery/           # Template gallery
│   │   └── layout.tsx         # Root layout
│   ├── components/            # React components
│   │   ├── editor/            # Editor-specific components
│   │   ├── templates/         # Template components
│   │   ├── ui/               # Reusable UI components
│   │   └── preview/          # Preview components
│   ├── lib/                   # Utility libraries
│   │   ├── mediawiki/        # MediaWiki-specific logic
│   │   ├── css-generator/    # CSS generation engine
│   │   ├── template-engine/  # Template conversion logic
│   │   └── database/         # Database utilities
│   ├── types/                 # TypeScript type definitions
│   ├── hooks/                 # Custom React hooks
│   └── stores/               # Zustand stores
├── prisma/                    # Database schema and migrations
├── public/                    # Static assets
└── docs/                     # Documentation
```

## Architecture Overview

### Frontend Architecture
- **Next.js 14** with App Router for modern React development
- **TypeScript** for type safety and better developer experience
- **Tailwind CSS** for rapid, consistent styling
- **Zustand** for lightweight state management
- **React DnD Kit** for drag-and-drop functionality

### Backend Architecture
- **Next.js API Routes** for serverless backend functionality
- **Prisma ORM** with SQLite/PostgreSQL for data persistence
- **MediaWiki API integration** for template validation and preview

### Core Systems
1. **Visual Editor Engine** - Handles drag-and-drop template building
2. **Template Conversion Engine** - Converts visual designs to MediaWiki wikitext
3. **CSS Generation System** - Creates responsive CSS from visual styling
4. **Preview System** - Real-time template rendering simulation
5. **Component Library** - Pre-built MediaWiki template patterns

## Development Workflow

1. **Design Phase** - Use the visual editor to create template layouts
2. **Styling Phase** - Apply CSS styling through the visual interface
3. **Preview Phase** - Test templates in the real-time preview
4. **Export Phase** - Generate and download wikitext and CSS files
5. **Import Phase** - Load existing templates for modification

## Next Steps

After installing Node.js and setting up the project:

1. Run `npm run dev` to start the development server
2. Open [http://localhost:3000](http://localhost:3000) in your browser
3. Begin with the visual editor interface development
4. Implement the MediaWiki template conversion engine
5. Add CSS generation capabilities
6. Build the component library
7. Implement import/export functionality

## Contributing

This project follows modern web development best practices:
- TypeScript for type safety
- ESLint and Prettier for code quality
- Component-driven development
- Test-driven development with Jest and React Testing Library

## License

MIT License - see LICENSE file for details
