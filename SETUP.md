# MediaWiki Template Builder - Setup Guide

This guide will help you set up and run the MediaWiki Template Builder on your local machine.

## Prerequisites

Before you begin, ensure you have the following installed:

1. **Node.js** (v18 or higher) - [Download from nodejs.org](https://nodejs.org/)
2. **Git** - For version control
3. **VS Code** (recommended) - For development

## Installation Steps

### 1. Install Node.js

1. Go to [https://nodejs.org/](https://nodejs.org/)
2. Download the LTS version (recommended for most users)
3. Run the installer and follow the installation wizard
4. Restart your terminal/command prompt

### 2. Verify Installation

Open a new terminal/command prompt and run:

```bash
node --version
npm --version
```

You should see version numbers for both commands.

### 3. Install Dependencies

Navigate to your project directory and run:

```bash
# Install all project dependencies
npm install
```

This will install all the required packages listed in `package.json`.

### 4. Set Up Environment Variables

1. Copy the example environment file:
   ```bash
   copy .env.example .env.local
   ```

2. Edit `.env.local` and update the values as needed:
   ```env
   DATABASE_URL="mysql://299wiki:135790wW@localhost:3306/299wiki"
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key-here"
   ```

### 5. Set Up the Database

Initialize the database with Prisma:

```bash
# Generate Prisma client
npm run db:generate

# Create and migrate the database
npm run db:push
```

### 6. Start the Development Server

```bash
npm run dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

## Project Structure

```
mediawiki-template-builder/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── api/               # API routes
│   │   ├── editor/            # Visual editor page
│   │   ├── gallery/           # Template gallery
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # React components
│   │   ├── editor/            # Editor-specific components
│   │   ├── templates/         # Template components
│   │   ├── ui/               # Reusable UI components
│   │   └── preview/          # Preview components
│   ├── lib/                   # Utility libraries
│   │   ├── mediawiki/        # MediaWiki-specific logic
│   │   ├── css-generator/    # CSS generation engine
│   │   ├── template-engine/  # Template conversion logic
│   │   └── utils.ts          # General utilities
│   ├── types/                 # TypeScript type definitions
│   │   └── template.ts       # Template-related types
│   ├── hooks/                 # Custom React hooks
│   └── stores/               # Zustand stores
├── prisma/                    # Database schema and migrations
│   └── schema.prisma         # Database schema
├── public/                    # Static assets
├── docs/                     # Documentation
├── package.json              # Project dependencies
├── next.config.js            # Next.js configuration
├── tailwind.config.ts        # Tailwind CSS configuration
├── tsconfig.json             # TypeScript configuration
└── README.md                 # Project documentation
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push database schema changes
- `npm run db:studio` - Open Prisma Studio (database GUI)

## Development Workflow

### 1. Start Development

```bash
npm run dev
```

### 2. Open the Application

- **Home Page**: [http://localhost:3000](http://localhost:3000)
- **Visual Editor**: [http://localhost:3000/editor](http://localhost:3000/editor)
- **Template Gallery**: [http://localhost:3000/gallery](http://localhost:3000/gallery)

### 3. Database Management

View and edit your database using Prisma Studio:

```bash
npm run db:studio
```

This opens a web interface at [http://localhost:5555](http://localhost:5555).

## Key Features Implemented

### ✅ Completed Features

1. **Project Architecture** - Modern Next.js 14 setup with TypeScript
2. **UI Framework** - Tailwind CSS with custom MediaWiki-inspired design
3. **Database Schema** - Prisma ORM with SQLite for development
4. **Type System** - Comprehensive TypeScript types for templates
5. **MediaWiki Engine** - Core engine for converting designs to wikitext
6. **CSS Generator** - System for generating responsive CSS
7. **Basic Editor UI** - Visual editor interface with drag-and-drop placeholder
8. **Template Gallery** - Browse and preview templates
9. **Utility Functions** - Helper functions for validation and formatting

### 🚧 In Progress

1. **Visual Editor Framework** - Implementing drag-and-drop functionality
2. **Component Library** - Building pre-made template components
3. **Real-time Preview** - Live preview of template rendering
4. **Import/Export** - Template and CSS export functionality

### 📋 Planned Features

1. **Template Components** - Infoboxes, navboxes, citations, etc.
2. **Advanced Styling** - Visual CSS editor with live preview
3. **Template Validation** - MediaWiki syntax validation
4. **User Authentication** - Save and share templates
5. **Collaboration** - Multi-user template editing
6. **API Integration** - MediaWiki API for template testing

## Troubleshooting

### Common Issues

1. **Node.js not found**
   - Ensure Node.js is installed and added to your PATH
   - Restart your terminal after installation

2. **Permission errors on Windows**
   - Run terminal as Administrator
   - Or use `npx` instead of global npm packages

3. **Database connection issues**
   - Ensure the database file path is correct in `.env.local`
   - Run `npm run db:push` to recreate the database

4. **Port already in use**
   - Change the port in `package.json` dev script: `next dev -p 3001`
   - Or kill the process using port 3000

### Getting Help

1. Check the console for error messages
2. Ensure all dependencies are installed: `npm install`
3. Clear Next.js cache: `rm -rf .next` (or `rmdir /s .next` on Windows)
4. Restart the development server

## Next Steps

1. **Explore the Editor**: Visit `/editor` to see the visual interface
2. **Browse Templates**: Check out `/gallery` for template examples
3. **Read the Code**: Start with `src/types/template.ts` to understand the data structure
4. **Contribute**: The project is set up for easy development and extension

## Contributing

The project is structured for easy contribution:

- **Components**: Add new UI components in `src/components/`
- **Templates**: Create template patterns in `src/components/templates/`
- **Utilities**: Add helper functions in `src/lib/`
- **Types**: Extend type definitions in `src/types/`
- **Styles**: Customize appearance in `src/app/globals.css`

Happy building! 🚀
