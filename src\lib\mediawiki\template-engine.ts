// MediaWiki Template Engine - Converts visual designs to wikitext

import { Template, TemplateElement, TemplateParameter, WikitextOutput } from '@/types/template'

export class MediaWikiTemplateEngine {
  /**
   * Converts a visual template design to MediaWiki wikitext
   */
  static generateWikitext(template: Template): WikitextOutput {
    const templateCode = this.generateTemplateCode(template)
    const documentationPage = this.generateDocumentation(template)
    const categoryCode = this.generateCategoryCode(template)

    return {
      templateCode,
      documentationPage,
      categoryCode
    }
  }

  /**
   * Generates the main template code
   */
  private static generateTemplateCode(template: Template): string {
    const lines: string[] = []
    
    // Add template header with parameters
    lines.push('<includeonly>')
    
    // Generate the main template structure
    const templateBody = this.generateElementsCode(template.elements, template.parameters)
    lines.push(templateBody)
    
    // Add categories if any
    const categories = this.extractCategories(template.elements)
    if (categories.length > 0) {
      lines.push('')
      categories.forEach(category => {
        lines.push(`[[Category:${category}]]`)
      })
    }
    
    lines.push('</includeonly>')
    
    // Add noinclude section for documentation
    lines.push('<noinclude>')
    lines.push('{{documentation}}')
    lines.push('</noinclude>')
    
    return lines.join('\n')
  }

  /**
   * Generates code for template elements recursively
   */
  private static generateElementsCode(elements: TemplateElement[], parameters: TemplateParameter[]): string {
    return elements.map(element => this.generateElementCode(element, parameters)).join('\n')
  }

  /**
   * Generates code for a single template element
   */
  private static generateElementCode(element: TemplateElement, parameters: TemplateParameter[]): string {
    switch (element.type) {
      case 'container':
        return this.generateContainerCode(element, parameters)
      case 'text':
        return this.generateTextCode(element, parameters)
      case 'image':
        return this.generateImageCode(element, parameters)
      case 'table':
        return this.generateTableCode(element, parameters)
      case 'infobox':
        return this.generateInfoboxCode(element, parameters)
      case 'navbox':
        return this.generateNavboxCode(element, parameters)
      case 'parameter':
        return this.generateParameterCode(element, parameters)
      case 'conditional':
        return this.generateConditionalCode(element, parameters)
      default:
        return `<!-- Unknown element type: ${element.type} -->`
    }
  }

  /**
   * Generates container/div code
   */
  private static generateContainerCode(element: TemplateElement, parameters: TemplateParameter[]): string {
    const cssClass = element.properties.cssClass || `template-${element.id}`
    const children = element.children ? this.generateElementsCode(element.children, parameters) : ''
    
    return `<div class="${cssClass}">\n${children}\n</div>`
  }

  /**
   * Generates text element code
   */
  private static generateTextCode(element: TemplateElement, parameters: TemplateParameter[]): string {
    const text = element.properties.text || ''
    const tag = element.properties.tag || 'span'
    const cssClass = element.properties.cssClass || `text-${element.id}`
    
    // Replace parameter placeholders
    const processedText = this.replaceParameterPlaceholders(text, parameters)
    
    return `<${tag} class="${cssClass}">${processedText}</${tag}>`
  }

  /**
   * Generates image element code
   */
  private static generateImageCode(element: TemplateElement, parameters: TemplateParameter[]): string {
    const imageName = element.properties.imageName || '{{{image|}}}'
    const size = element.properties.size || '200px'
    const alt = element.properties.alt || '{{{image_alt|}}}'
    const caption = element.properties.caption || '{{{image_caption|}}}'
    
    if (caption) {
      return `[[File:${imageName}|${size}|alt=${alt}|${caption}]]`
    } else {
      return `[[File:${imageName}|${size}|alt=${alt}]]`
    }
  }

  /**
   * Generates table code
   */
  private static generateTableCode(element: TemplateElement, parameters: TemplateParameter[]): string {
    const cssClass = element.properties.cssClass || `table-${element.id}`
    const rows = element.properties.rows || []
    
    let tableCode = `{| class="${cssClass}"\n`
    
    rows.forEach((row: any[], index: number) => {
      if (index === 0 && element.properties.hasHeader) {
        tableCode += '! ' + row.join(' !! ') + '\n'
      } else {
        tableCode += '| ' + row.join(' || ') + '\n'
      }
      if (index < rows.length - 1) {
        tableCode += '|-\n'
      }
    })
    
    tableCode += '|}'
    return tableCode
  }

  /**
   * Generates infobox code
   */
  private static generateInfoboxCode(element: TemplateElement, parameters: TemplateParameter[]): string {
    const cssClass = element.properties.cssClass || 'infobox'
    const title = element.properties.title || '{{{name|{{{title|}}}}}}'
    const fields = element.properties.fields || []
    
    let infoboxCode = `{| class="${cssClass}"\n`
    
    // Title row
    if (title) {
      infoboxCode += `! colspan="2" | ${title}\n|-\n`
    }
    
    // Field rows
    fields.forEach((field: any) => {
      const label = field.label || ''
      const value = field.value || `{{{${field.parameter}|}}}`
      const condition = field.condition ? `{{#if:{{{${field.parameter}|}}}|` : ''
      const conditionEnd = field.condition ? '}}' : ''
      
      infoboxCode += `${condition}! ${label}\n| ${value}\n|-\n${conditionEnd}`
    })
    
    infoboxCode += '|}'
    return infoboxCode
  }

  /**
   * Generates navigation box code
   */
  private static generateNavboxCode(element: TemplateElement, parameters: TemplateParameter[]): string {
    const title = element.properties.title || '{{{title|}}}'
    const groups = element.properties.groups || []
    
    let navboxCode = `{{Navbox\n| name = ${element.properties.name || 'Navbox'}\n| title = ${title}\n`
    
    groups.forEach((group: any, index: number) => {
      const groupNum = index + 1
      navboxCode += `| group${groupNum} = ${group.title}\n`
      navboxCode += `| list${groupNum} = ${group.items.join(' • ')}\n`
    })
    
    navboxCode += '}}'
    return navboxCode
  }

  /**
   * Generates parameter reference code
   */
  private static generateParameterCode(element: TemplateElement, parameters: TemplateParameter[]): string {
    const paramName = element.properties.parameterName || ''
    const defaultValue = element.properties.defaultValue || ''
    const format = element.properties.format || 'raw'
    
    let paramCode = `{{{${paramName}`
    if (defaultValue) {
      paramCode += `|${defaultValue}`
    }
    paramCode += '}}}'
    
    // Apply formatting if specified
    if (format === 'bold') {
      paramCode = `'''${paramCode}'''`
    } else if (format === 'italic') {
      paramCode = `''${paramCode}''`
    } else if (format === 'link') {
      paramCode = `[[${paramCode}]]`
    }
    
    return paramCode
  }

  /**
   * Generates conditional code
   */
  private static generateConditionalCode(element: TemplateElement, parameters: TemplateParameter[]): string {
    const condition = element.properties.condition || ''
    const trueContent = element.properties.trueContent || ''
    const falseContent = element.properties.falseContent || ''
    
    let conditionalCode = `{{#if:${condition}|${trueContent}`
    if (falseContent) {
      conditionalCode += `|${falseContent}`
    }
    conditionalCode += '}}'
    
    return conditionalCode
  }

  /**
   * Replaces parameter placeholders in text
   */
  private static replaceParameterPlaceholders(text: string, parameters: TemplateParameter[]): string {
    let processedText = text
    
    // Replace {{param}} with {{{param|}}}
    processedText = processedText.replace(/\{\{(\w+)\}\}/g, '{{{$1|}}}')
    
    return processedText
  }

  /**
   * Extracts categories from template elements
   */
  private static extractCategories(elements: TemplateElement[]): string[] {
    const categories: string[] = []
    
    const extractFromElement = (element: TemplateElement) => {
      if (element.type === 'category' && element.properties.categoryName) {
        categories.push(element.properties.categoryName)
      }
      
      if (element.children) {
        element.children.forEach(extractFromElement)
      }
    }
    
    elements.forEach(extractFromElement)
    return categories
  }

  /**
   * Generates template documentation
   */
  private static generateDocumentation(template: Template): string {
    const lines: string[] = []
    
    lines.push('== Usage ==')
    lines.push(`{{${template.name}`)
    
    template.parameters.forEach(param => {
      const required = param.required ? '' : ' <!-- optional -->'
      lines.push(`| ${param.name} = ${param.description}${required}`)
    })
    
    lines.push('}}')
    lines.push('')
    
    lines.push('== Parameters ==')
    template.parameters.forEach(param => {
      const requiredText = param.required ? 'Required' : 'Optional'
      lines.push(`; ${param.name} : ${param.description} (${requiredText})`)
    })
    
    if (template.metadata.examples.length > 0) {
      lines.push('')
      lines.push('== Examples ==')
      template.metadata.examples.forEach((example, index) => {
        lines.push(`=== ${example.name} ===`)
        lines.push(example.description)
        lines.push('<pre>')
        lines.push(`{{${template.name}`)
        Object.entries(example.parameters).forEach(([key, value]) => {
          lines.push(`| ${key} = ${value}`)
        })
        lines.push('}}')
        lines.push('</pre>')
        lines.push('')
      })
    }
    
    return lines.join('\n')
  }

  /**
   * Generates category code for the template
   */
  private static generateCategoryCode(template: Template): string {
    const categories = [`[[Category:${template.category} templates]]`]
    
    if (template.metadata.tags) {
      template.metadata.tags.forEach(tag => {
        categories.push(`[[Category:Templates with ${tag}]]`)
      })
    }
    
    return categories.join('\n')
  }
}
