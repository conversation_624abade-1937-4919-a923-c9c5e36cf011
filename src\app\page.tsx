import Link from 'next/link'
import { FileText, Palette, Eye, Download, Upload, Zap } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-mw-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-mw-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">
                MediaWiki Template Builder
              </h1>
            </div>
            <nav className="flex space-x-4">
              <Link href="/editor" className="btn-primary">
                Start Building
              </Link>
              <Link href="/gallery" className="btn-secondary">
                Template Gallery
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-gray-900 sm:text-6xl">
            Build Beautiful
            <span className="text-mw-blue-600"> MediaWiki Templates</span>
          </h2>
          <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
            Create stunning MediaWiki templates and CSS through an intuitive visual interface. 
            No more wrestling with complex wikitext syntax.
          </p>
          <div className="mt-10 flex justify-center gap-4">
            <Link href="/editor" className="btn-primary text-lg px-8 py-3">
              <Zap className="w-5 h-5 mr-2" />
              Start Creating
            </Link>
            <Link href="/gallery" className="btn-secondary text-lg px-8 py-3">
              <Eye className="w-5 h-5 mr-2" />
              View Examples
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="card text-center">
            <div className="w-12 h-12 bg-mw-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <FileText className="w-6 h-6 text-mw-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Visual Template Designer
            </h3>
            <p className="text-gray-600">
              Drag-and-drop interface for building MediaWiki templates without writing raw wikitext.
            </p>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Palette className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              CSS Generation
            </h3>
            <p className="text-gray-600">
              Automatically generates clean, responsive CSS code that works perfectly with your templates.
            </p>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Eye className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Real-time Preview
            </h3>
            <p className="text-gray-600">
              See exactly how your templates will look when rendered in MediaWiki as you build them.
            </p>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Download className="w-6 h-6 text-orange-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Export Templates
            </h3>
            <p className="text-gray-600">
              Export both wikitext templates and CSS code ready to use in your MediaWiki installation.
            </p>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Upload className="w-6 h-6 text-red-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Import Existing
            </h3>
            <p className="text-gray-600">
              Import and modify existing MediaWiki templates through the visual interface.
            </p>
          </div>

          <div className="card text-center">
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Zap className="w-6 h-6 text-indigo-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Template Library
            </h3>
            <p className="text-gray-600">
              Pre-built components for infoboxes, navigation boxes, citations, and more common patterns.
            </p>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <div className="bg-white rounded-2xl shadow-xl p-8 max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to revolutionize your MediaWiki workflow?
            </h3>
            <p className="text-xl text-gray-600 mb-8">
              Join thousands of wiki editors who have simplified their template creation process.
            </p>
            <Link href="/editor" className="btn-primary text-lg px-8 py-3">
              Get Started Now
            </Link>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2024 MediaWiki Template Builder. Built with ❤️ for the wiki community.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
